package cloud.server.ai.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "AI助手", description = "AI助手相关接口")
@RestController
@RequestMapping("/ai/assistant")
public class AssistantController {
    @Operation(summary = "获取AI助手列表", description = "获取AI助手列表")
    @RequestMapping("/list")
    public void list() {
        System.out.println("获取AI助手列表");
    }

    @Operation(summary = "创建AI助手", description = "创建AI助手")
    @RequestMapping("/create")
    public void create() {
        System.out.println("<UNK>AI<UNK>");
    }

    @Operation(summary = "获取AI助手详情", description = "获取AI助手详情")
    @RequestMapping("/detail/{id}")
    public void detail(@PathVariable String id) {
        System.out.println("<UNK>AI<UNK>");
    }

    @Operation(summary = "删除AI助手", description = "删除AI助手")
    @RequestMapping("/delete/{id}")
    public void delete(@PathVariable String id) {
        System.out.println("<UNK>AI<UNK>");
    }
}
