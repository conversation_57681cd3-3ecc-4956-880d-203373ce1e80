//package cloud.server.ai.controller;
//
//import cloud.server.ai.common.ApiResponse;
//import cloud.server.ai.dto.ChatDto;
//import cloud.server.ai.service.ChatService;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import jakarta.annotation.Resource;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.ai.chat.client.ChatClient;
//import org.springframework.http.MediaType;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.*;
//import reactor.core.publisher.Flux;
//
//@Tag(name = "AI聊天", description = "AI聊天相关接口")
//@RestController
//@Slf4j
//@RequestMapping("/ai/chat")
//public class ChatController {
//    @Resource
//    private ChatService chatService;
//
//    private final ChatClient chatClient;
//
//    public ChatController(ChatClient.Builder chatClientBuilder) {
//        this.chatClient = chatClientBuilder.build();
//    }
//
//    @Operation(summary = "简单AI聊天接口")
//    @GetMapping("/simple")
//    public String simple(String query) {
//        return chatClient.prompt(query).call().content();
//    }
//
//    @Operation(summary = "简单AI聊天接口")
//    @GetMapping(value = "/simple/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
//    public Flux<String> simpleStream(String query) {
//        return chatClient.prompt(query).stream().content();
//    }
//
//    @Operation(summary = "统一AI聊天接口")
//    @PostMapping
//    public ApiResponse<?> chat(@RequestBody @Validated ChatDto chatDto) {
//        return ApiResponse.success(chatService.chat(chatDto));
//    }
//
//    @Operation(summary = "流式聊天接口")
//    @PostMapping("/stream")
//    public void stream() {
//        System.out.println("<UNK>");
//    }
//}
