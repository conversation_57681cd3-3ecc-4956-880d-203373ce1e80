package cloud.server.ai.controller;

import cloud.server.ai.common.ApiResponse;
import cloud.server.ai.dto.UpdateProviderStatusDto;
import cloud.server.ai.service.ProviderService;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/ai/provider")
public class ProviderController {
    @Resource
    private ProviderService providerService;

    @Operation(summary = "获取提供商类型")
    @GetMapping("/type")
    public void getProviderType() {
        // 模型枚举值

    }

    @Operation(summary = "获取提供商列表")
    @GetMapping("/list")
    public ApiResponse<?> getProviderList() {
        return ApiResponse.success(providerService.getProviderList());
    }

    @Operation(summary = "创建提供商")
    @PostMapping("/create")
    public void createProvider() {

    }

    @Operation(summary = "更新提供商")
    @PostMapping("/update")
    public void updateProvider() {}

    @Operation(summary = "更新提供商状态")
    @PostMapping("/update/status")
    public void updateProviderStatus(@Validated @RequestBody UpdateProviderStatusDto updateProviderStatusDto) {
        providerService.updateProviderStatus(updateProviderStatusDto);
    }
}
