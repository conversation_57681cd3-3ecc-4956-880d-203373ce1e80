//package cloud.server.ai.langchain.factory;
//
//import cloud.server.ai.langchain.pojo.ChatModelConfig;
//import dev.langchain4j.model.chat.ChatLanguageModel;
//
///**
// * 模型工厂 接口
// */
////public interface ModelFactory {
////    /**
////     * 创建对话模型
////     * @param chatModelConfig 对话模型配置
////     * @return 对话模型
////     */
////    ChatLanguageModel createChatModel(ChatModelConfig chatModelConfig);
////}
