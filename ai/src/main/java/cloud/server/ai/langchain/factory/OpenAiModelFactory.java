//package cloud.server.ai.langchain.factory;
//
//import cloud.server.ai.langchain.pojo.ChatModelConfig;
////import cloud.server.ai.langchain.provider.ModelProvider;
//import dev.langchain4j.model.chat.ChatLanguageModel;
//import dev.langchain4j.model.openai.OpenAiChatModel;
//import org.springframework.beans.factory.InitializingBean;
//import org.springframework.context.annotation.Primary;
//import org.springframework.stereotype.Service;
//
//import java.time.Duration;
//
///**
// * OpenAI 模型工厂 - 实现 ModelFactory 接口
// */
////@Service
////@Primary
////public class OpenAiModelFactory implements ModelFactory, InitializingBean {
////    public static final String PROVIDER = "OPEN_AI";
////    public static final String DEFAULT_BASE_URL = "https://api.openai.com/v1";
////    public static final String DEFAULT_MODEL_NAME = "gpt-4o-mini";
////
////    @Override
////    public void afterPropertiesSet() throws Exception {
////        ModelProvider.registerModelFactory(PROVIDER, this);
////    }
////
////    @Override
////    public ChatLanguageModel createChatModel(ChatModelConfig chatModelConfig) {
////        return OpenAiChatModel.builder().baseUrl(chatModelConfig.getBaseUrl())
////                .modelName(chatModelConfig.getModelName()).apiKey(chatModelConfig.keyDecrypt())
////                .temperature(chatModelConfig.getTemperature()).topP(chatModelConfig.getTopP())
////                .maxRetries(chatModelConfig.getMaxRetries())
////                .timeout(Duration.ofSeconds(chatModelConfig.getTimeOut()))
////                .logRequests(chatModelConfig.getLogRequests())
////                .logResponses(chatModelConfig.getLogResponses()).build();
////    }
////}
