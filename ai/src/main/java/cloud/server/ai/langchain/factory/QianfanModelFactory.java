//package cloud.server.ai.langchain.factory;
//
//import cloud.server.ai.langchain.pojo.ChatModelConfig;
//import cloud.server.ai.langchain.provider.ModelProvider;
//import dev.langchain4j.model.chat.ChatLanguageModel;
//import dev.langchain4j.model.qianfan.QianfanChatModel;
//import org.springframework.beans.factory.InitializingBean;
//import org.springframework.stereotype.Service;
//
///**
// * 千帆模型工厂 - 实现 ModelFactory 接口
// */
////@Service
////public class QianfanModelFactory implements ModelFactory, InitializingBean {
////    public static final String PROVIDER = "QIANFAN";
////    public static final String DEFAULT_BASE_URL = "https://aip.baidubce.com";
////    public static final String DEFAULT_MODEL_NAME = "Llama-2-70b-chat";
////
////    @Override
////    public void afterPropertiesSet() throws Exception {
////        ModelProvider.registerModelFactory(PROVIDER, this);
////    }
////
////    @Override
////    public ChatLanguageModel createChatModel(ChatModelConfig chatModelConfig) {
////        return QianfanChatModel.builder().baseUrl(chatModelConfig.getBaseUrl())
////                .apiKey(chatModelConfig.getApiKey()).secretKey(chatModelConfig.getSecretKey())
////                .endpoint(chatModelConfig.getEndpoint()).modelName(chatModelConfig.getModelName())
////                .temperature(chatModelConfig.getTemperature()).topP(chatModelConfig.getTopP())
////                .maxRetries(chatModelConfig.getMaxRetries()).logRequests(chatModelConfig.getLogRequests())
////                .logResponses(chatModelConfig.getLogResponses()).build();
////    }
////}
