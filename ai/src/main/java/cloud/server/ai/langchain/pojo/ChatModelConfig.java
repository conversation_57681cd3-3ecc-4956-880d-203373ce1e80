package cloud.server.ai.langchain.pojo;

import cloud.server.ai.util.AESEncryptionUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChatModelConfig {
    @Schema(description = "对话模型提供者：openai、azure、google等")
    private String provider;
    
    @Schema(description = "基础URL")
    private String baseUrl;
    
    @Schema(description = "API密钥")
    private String apiKey;
    
    @Schema(description = "API版本")
    private String apiVersion;
    
    @Schema(description = "模型名称")
    private String modelName;
    
    @Schema(description = "温度值")
    private Double temperature = 0.0d;
    
    @Schema(description = "超时时间")
    private Long timeOut = 60L;
    
    @Schema(description = "endpoint")
    private String endpoint;
    
    @Schema(description = "secretKey")
    private String secretKey;
    
    @Schema(description = "topP")
    private Double topP;
    
    @Schema(description = "最大重试次数")
    private Integer maxRetries = 3;
    
    @Schema(description = "是否记录请求和响应")
    private Boolean logRequests = false;
    
    @Schema(description = "是否记录响应")
    private Boolean logResponses = false;
    
    @Schema(description = "是否启用搜索")
    private Boolean enableSearch = false;

    /**
     * 解密API密钥
     * @return 解密后的API密钥
     */
    public String keyDecrypt() {
        return AESEncryptionUtil.aesDecryptECB(getApiKey());
    }
}
