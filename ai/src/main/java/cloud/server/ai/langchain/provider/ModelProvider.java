//package cloud.server.ai.langchain.provider;
//
////import cloud.server.ai.langchain.factory.ModelFactory;
//import cloud.server.ai.langchain.pojo.ChatModelConfig;
//import cn.hutool.core.util.StrUtil;
//import dev.langchain4j.model.chat.ChatLanguageModel;
//
//import java.util.HashMap;
//import java.util.Map;
//import java.util.Objects;
//
///**
// * 大模型提供者 - 用于加载大模型
// */
////public class ModelProvider {
////    // 模型工厂映射
////    private static final Map<String, ModelFactory> factories = new HashMap<>();
////
////    // 默认加载的模型工厂
////    public static final ChatModelConfig DEFAULT_CHAT_MODEL = ChatModelConfig.builder()
////            .provider("OPEN_AI")
////            .baseUrl("https://api.openai.com/v1")
////            .apiKey("demo")
////            .apiVersion("1.0")
////            .modelName("gpt-4o-mini")
////            .temperature(0.0)
////            .timeOut(60L)
////            .build();
////
////    // 注册模型工厂
////    public static void registerModelFactory(String provider, ModelFactory modelFactory) {
////        factories.put(provider, modelFactory);
////    }
////
////    /**
////     * 通过对话模型配置加载对话模型
////     * @param chatModelConfig 对话模型配置
////     * @return 对话模型
////     */
////    public static ChatLanguageModel getChatModel(ChatModelConfig chatModelConfig) {
////        // 如果没有配置，使用默认配置
////        if (Objects.isNull(chatModelConfig) || StrUtil.isEmpty(chatModelConfig.getProvider())) {
////            chatModelConfig = DEFAULT_CHAT_MODEL;
////        }
////        // 获取对应的模型工厂
////        if (factories.containsKey(chatModelConfig.getProvider())) {
////            ModelFactory modelFactory = factories.get(chatModelConfig.getProvider());
////            return modelFactory.createChatModel(chatModelConfig);
////        } else {
////            throw new RuntimeException("Unsupported ChatLanguageModel provider: " + chatModelConfig.getProvider());
////        }
////    }
////}
