# CORS 实现方式对比指南

本项目提供了三种不同的CORS实现方式，你可以根据需要选择其中一种。

## 1. 配置类方式 (CorsConfig.java) - 已禁用

**文件位置**: `config/CorsConfig.java`

**特点**:
- 使用Spring WebFlux的`CorsWebFilter`
- 声明式配置，简洁明了
- 适合简单的CORS需求
- 配置相对固定，不易动态调整

**使用方法**:
```java
@Configuration
public class CorsConfig {
    @Bean
    public CorsWebFilter corsWebFilter() {
        CorsConfiguration config = new CorsConfiguration();
        config.addAllowedOriginPattern("*");
        config.setAllowCredentials(true);
        config.addAllowedHeader("*");
        config.addAllowedMethod("*");
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", config);
        return new CorsWebFilter(source);
    }
}
```

## 2. 简单过滤器方式 (CorsFilter.java) - 当前启用

**文件位置**: `filter/CorsFilter.java`

**特点**:
- 使用Spring Cloud Gateway的`GlobalFilter`
- 编程式实现，灵活性高
- 可以添加自定义逻辑
- 与其他过滤器集成更好

**优势**:
- 更好的控制权
- 可以根据请求动态调整CORS策略
- 与Gateway过滤器链集成
- 可以添加日志、监控等功能

## 3. 可配置过滤器方式 (ConfigurableCorsFilter.java) - 可选

**文件位置**: `filter/ConfigurableCorsFilter.java`

**特点**:
- 支持通过配置文件自定义设置
- 使用`@ConfigurationProperties`注解
- 最灵活的实现方式
- 支持运行时配置更新

**配置示例** (application.yml):
```yaml
cors:
  allowed-origins:
    - "http://localhost:3000"
    - "https://yourdomain.com"
  allowed-headers:
    - "Origin"
    - "Content-Type"
    - "Authorization"
  allowed-methods:
    - "GET"
    - "POST"
    - "PUT"
    - "DELETE"
  allow-credentials: true
  max-age: 3600
```

## 如何切换实现方式

### 切换到配置类方式:
1. 在`CorsConfig.java`中取消注释`@Configuration`和`@Bean`
2. 删除或注释掉`CorsFilter.java`中的`@Component`注解

### 切换到可配置过滤器方式:
1. 删除或注释掉`CorsFilter.java`中的`@Component`注解
2. 确保`ConfigurableCorsFilter.java`有`@Component`注解
3. 在`application.yml`中添加cors配置

### 保持当前简单过滤器方式:
- 无需修改，当前已启用

## 性能对比

| 实现方式 | 性能 | 灵活性 | 配置复杂度 | 推荐场景 |
|---------|------|--------|-----------|----------|
| CorsConfig | 高 | 低 | 低 | 简单项目，固定配置 |
| CorsFilter | 高 | 中 | 中 | 需要自定义逻辑 |
| ConfigurableCorsFilter | 中 | 高 | 高 | 复杂项目，动态配置 |

## 注意事项

1. **同时只能启用一种方式**，否则会产生冲突
2. **Credentials和Origin的关系**:
   - 当`allowCredentials=true`时，不能设置`Origin=*`
   - 需要指定具体的域名
3. **预检请求处理**:
   - 所有实现都正确处理了OPTIONS预检请求
4. **过滤器顺序**:
   - CORS过滤器应该有较高优先级(`@Order(-1)`)

## 推荐使用

- **开发环境**: 使用`CorsFilter`，简单直接
- **生产环境**: 使用`ConfigurableCorsFilter`，便于运维配置
- **简单项目**: 使用`CorsConfig`，配置简洁

当前项目使用的是**CorsFilter**方式，如需切换请参考上述说明。
