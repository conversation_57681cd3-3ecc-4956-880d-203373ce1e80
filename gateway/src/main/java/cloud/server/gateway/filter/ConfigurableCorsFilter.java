package cloud.server.gateway.filter;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.Arrays;
import java.util.List;

/**
 * [cors 跨域] 可配置过滤器
 * 支持通过配置文件自定义CORS设置
 * 使用方式：在application.yml中添加cors配置
 */
@Component
@ConfigurationProperties(prefix = "cors")
@Order(-1)
public class ConfigurableCorsFilter implements GlobalFilter {
    
    // 允许的域名列表
    private List<String> allowedOrigins = Arrays.asList("*");
    
    // 允许的请求头列表
    private List<String> allowedHeaders = Arrays.asList(
            "Origin", "Content-Type", "Accept", "Authorization", 
            "X-Requested-With", "X-User-Info", "Cache-Control"
    );
    
    // 允许的请求方法列表
    private List<String> allowedMethods = Arrays.asList(
            "GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH"
    );
    
    // 暴露给客户端的响应头
    private List<String> exposedHeaders = Arrays.asList(
            "Content-Length", "Access-Control-Allow-Origin", 
            "Access-Control-Allow-Headers", "Cache-Control", 
            "Content-Language", "Content-Type"
    );
    
    // 是否允许携带凭证
    private boolean allowCredentials = true;
    
    // 预检请求缓存时间（秒）
    private long maxAge = 3600;
    
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        ServerHttpResponse response = exchange.getResponse();
        HttpHeaders headers = response.getHeaders();
        
        // 获取请求的Origin
        String origin = request.getHeaders().getOrigin();
        
        // 设置CORS响应头
        setCorsHeaders(headers, origin);
        
        // 处理预检请求（OPTIONS）
        if (request.getMethod() == HttpMethod.OPTIONS) {
            response.setStatusCode(HttpStatus.OK);
            return response.setComplete();
        }
        
        // 继续执行后续过滤器
        return chain.filter(exchange);
    }
    
    /**
     * 设置CORS相关的响应头
     */
    private void setCorsHeaders(HttpHeaders headers, String origin) {
        // 设置允许的源
        if (StringUtils.hasText(origin) && isOriginAllowed(origin)) {
            headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, origin);
            if (allowCredentials) {
                headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_CREDENTIALS, "true");
            }
        } else if (allowedOrigins.contains("*")) {
            headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*");
            // 注意：当Origin为*时，不能设置Credentials为true
            if (allowCredentials && !StringUtils.hasText(origin)) {
                // 如果没有具体的origin但需要credentials，则不设置*
                return;
            }
        }
        
        // 设置允许的请求头
        if (!allowedHeaders.isEmpty()) {
            headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, String.join(", ", allowedHeaders));
        }
        
        // 设置允许的请求方法
        if (!allowedMethods.isEmpty()) {
            headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, String.join(", ", allowedMethods));
        }
        
        // 设置预检请求的缓存时间
        headers.add(HttpHeaders.ACCESS_CONTROL_MAX_AGE, String.valueOf(maxAge));
        
        // 设置暴露给客户端的响应头
        if (!exposedHeaders.isEmpty()) {
            headers.add(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, String.join(", ", exposedHeaders));
        }
    }
    
    /**
     * 检查Origin是否被允许
     */
    private boolean isOriginAllowed(String origin) {
        return allowedOrigins.contains("*") || allowedOrigins.contains(origin);
    }
    
    // Getter and Setter methods for configuration properties
    public List<String> getAllowedOrigins() {
        return allowedOrigins;
    }
    
    public void setAllowedOrigins(List<String> allowedOrigins) {
        this.allowedOrigins = allowedOrigins;
    }
    
    public List<String> getAllowedHeaders() {
        return allowedHeaders;
    }
    
    public void setAllowedHeaders(List<String> allowedHeaders) {
        this.allowedHeaders = allowedHeaders;
    }
    
    public List<String> getAllowedMethods() {
        return allowedMethods;
    }
    
    public void setAllowedMethods(List<String> allowedMethods) {
        this.allowedMethods = allowedMethods;
    }
    
    public List<String> getExposedHeaders() {
        return exposedHeaders;
    }
    
    public void setExposedHeaders(List<String> exposedHeaders) {
        this.exposedHeaders = exposedHeaders;
    }
    
    public boolean isAllowCredentials() {
        return allowCredentials;
    }
    
    public void setAllowCredentials(boolean allowCredentials) {
        this.allowCredentials = allowCredentials;
    }
    
    public long getMaxAge() {
        return maxAge;
    }
    
    public void setMaxAge(long maxAge) {
        this.maxAge = maxAge;
    }
}
