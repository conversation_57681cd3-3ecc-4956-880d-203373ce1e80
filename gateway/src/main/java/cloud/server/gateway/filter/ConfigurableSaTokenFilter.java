package cloud.server.gateway.filter;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.reactor.context.SaReactorSyncHolder;
import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.util.SaResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.annotation.Order;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;

/**
 * [Sa-Token 权限认证] 可配置过滤器
 * 支持通过配置文件自定义认证设置
 */
@Slf4j
@Data
@Component
@ConfigurationProperties(prefix = "auth")
@Order(0) // 设置在CORS过滤器之后执行
public class ConfigurableSaTokenFilter implements GlobalFilter {
    
    private final AntPathMatcher pathMatcher = new AntPathMatcher();
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    // 排除的URI列表
    private List<String> excludeUris = Arrays.asList("/api/auth/**", "/favicon.ico");
    
    // 包含的URI列表（默认全部）
    private List<String> includeUris = Arrays.asList("/**");
    
    // 是否启用认证
    private boolean enabled = true;
    
    // 自定义错误消息
    private String unauthorizedMessage = "未授权";
    private String serverErrorMessage = "服务器内部错误";
    
    // HTTP状态码配置
    private int unauthorizedCode = HttpStatus.UNAUTHORIZED.value();
    private int serverErrorCode = HttpStatus.INTERNAL_SERVER_ERROR.value();
    
    // 是否记录认证日志
    private boolean logEnabled = true;
    
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        // 如果认证被禁用，直接通过
        if (!enabled) {
            return chain.filter(exchange);
        }
        
        ServerHttpRequest request = exchange.getRequest();
        String path = request.getURI().getPath();
        
        if (logEnabled) {
            log.debug("ConfigurableSaTokenFilter processing path: {}", path);
        }
        
        // 检查是否为包含路径
        if (!isIncludedPath(path)) {
            if (logEnabled) {
                log.debug("Path {} is not included in authentication", path);
            }
            return chain.filter(exchange);
        }
        
        // 检查是否为排除路径
        if (isExcludedPath(path)) {
            if (logEnabled) {
                log.debug("Path {} is excluded from authentication", path);
            }
            return chain.filter(exchange);
        }
        
        // 使用 SaReactorSyncHolder 来同步Sa-Token的上下文
        return SaReactorSyncHolder.syncContext(exchange, () -> {
            try {
                // 执行登录检查
                StpUtil.checkLogin();
                
                if (logEnabled) {
                    log.debug("Authentication successful for path: {}", path);
                }
                
                // 认证成功，继续执行后续过滤器
                return chain.filter(exchange);
                
            } catch (NotLoginException e) {
                if (logEnabled) {
                    log.warn("Authentication failed for path: {}, reason: {}", path, e.getMessage());
                }
                return handleAuthenticationError(exchange, e);
            } catch (Exception e) {
                if (logEnabled) {
                    log.error("Unexpected error during authentication for path: {}", path, e);
                }
                return handleGenericError(exchange, e);
            }
        });
    }
    
    /**
     * 检查路径是否在包含列表中
     */
    private boolean isIncludedPath(String path) {
        if (includeUris == null || includeUris.isEmpty()) {
            return true; // 默认包含所有路径
        }
        
        return includeUris.stream()
                .anyMatch(pattern -> pathMatcher.match(pattern, path));
    }
    
    /**
     * 检查路径是否在排除列表中
     */
    private boolean isExcludedPath(String path) {
        if (excludeUris == null || excludeUris.isEmpty()) {
            return false;
        }
        
        return excludeUris.stream()
                .anyMatch(pattern -> pathMatcher.match(pattern, path));
    }
    
    /**
     * 处理认证失败错误
     */
    private Mono<Void> handleAuthenticationError(ServerWebExchange exchange, NotLoginException e) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.valueOf(unauthorizedCode));
        response.getHeaders().add("Content-Type", MediaType.APPLICATION_JSON_VALUE);
        
        SaResult result = SaResult.error(unauthorizedMessage).setCode(unauthorizedCode);
        
        return writeResponse(response, result);
    }
    
    /**
     * 处理通用错误
     */
    private Mono<Void> handleGenericError(ServerWebExchange exchange, Exception e) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.valueOf(serverErrorCode));
        response.getHeaders().add("Content-Type", MediaType.APPLICATION_JSON_VALUE);
        
        String errorMessage = serverErrorMessage;
        if (logEnabled) {
            errorMessage = e.getMessage(); // 在开启日志时显示详细错误信息
        }
        
        SaResult result = SaResult.error(errorMessage).setCode(serverErrorCode);
        
        return writeResponse(response, result);
    }
    
    /**
     * 写入响应数据
     */
    private Mono<Void> writeResponse(ServerHttpResponse response, SaResult result) {
        try {
            String json = objectMapper.writeValueAsString(result);
            DataBuffer buffer = response.bufferFactory().wrap(json.getBytes(StandardCharsets.UTF_8));
            return response.writeWith(Mono.just(buffer));
        } catch (JsonProcessingException e) {
            if (logEnabled) {
                log.error("Error serializing response", e);
            }
            // 如果序列化失败，返回简单的错误信息
            String fallbackJson = "{\"code\":" + result.getCode() + ",\"msg\":\"" + result.getMsg() + "\"}";
            DataBuffer buffer = response.bufferFactory().wrap(fallbackJson.getBytes(StandardCharsets.UTF_8));
            return response.writeWith(Mono.just(buffer));
        }
    }
}
