package cloud.server.gateway.filter;

import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * [cors 跨域] 过滤器
 * 将原来的CorsConfig配置类改写为GlobalFilter实现
 */
@Component
@Order(-1) // 设置较高优先级，确保在其他过滤器之前执行
public class CorsFilter implements GlobalFilter {
    
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        ServerHttpResponse response = exchange.getResponse();
        HttpHeaders headers = response.getHeaders();
        
        // 获取请求的Origin
        String origin = request.getHeaders().getOrigin();
        
        // 设置CORS响应头
        if (origin != null) {
            headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, origin);
        } else {
            headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*");
        }
        
        // 允许携带凭证（cookies）
        headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_CREDENTIALS, "true");
        
        // 允许的请求头
        headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, "*");
        
        // 允许的请求方法
        headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, "*");
        
        // 预检请求的缓存时间
        headers.add(HttpHeaders.ACCESS_CONTROL_MAX_AGE, "3600");
        
        // 处理预检请求（OPTIONS）
        if (request.getMethod() == HttpMethod.OPTIONS) {
            response.setStatusCode(HttpStatus.OK);
            return response.setComplete();
        }
        
        // 继续执行后续过滤器
        return chain.filter(exchange);
    }
}
