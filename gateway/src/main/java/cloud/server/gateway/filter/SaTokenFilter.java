package cloud.server.gateway.filter;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.reactor.context.SaReactorSyncHolder;
import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.util.SaResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.annotation.Order;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * [Sa-Token 权限认证] 过滤器
 * 将原来的SaTokenConfig配置类改写为GlobalFilter实现
 */
@Slf4j
@Component
@Order(0) // 设置在CORS过滤器之后执行
public class SaTokenFilter implements GlobalFilter {
    
    private final AntPathMatcher pathMatcher = new AntPathMatcher();
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Value("${auth.excludeUris:}")
    private List<String> excludeUris;
    
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String path = request.getURI().getPath();
        
        log.debug("SaTokenFilter processing path: {}", path);
        
        // 检查是否为排除路径
        if (isExcludedPath(path)) {
            log.debug("Path {} is excluded from authentication", path);
            return chain.filter(exchange);
        }
        
        // 使用 SaReactorSyncHolder 来同步Sa-Token的上下文
        return SaReactorSyncHolder.syncContext(exchange, () -> {
            try {
                // 执行登录检查
                StpUtil.checkLogin();
                log.debug("Authentication successful for path: {}", path);
                
                // 认证成功，继续执行后续过滤器
                return chain.filter(exchange);
                
            } catch (NotLoginException e) {
                log.warn("Authentication failed for path: {}, reason: {}", path, e.getMessage());
                return handleAuthenticationError(exchange, e);
            } catch (Exception e) {
                log.error("Unexpected error during authentication for path: {}", path, e);
                return handleGenericError(exchange, e);
            }
        });
    }
    
    /**
     * 检查路径是否在排除列表中
     */
    private boolean isExcludedPath(String path) {
        if (excludeUris == null || excludeUris.isEmpty()) {
            return false;
        }
        
        return excludeUris.stream()
                .anyMatch(pattern -> pathMatcher.match(pattern, path));
    }
    
    /**
     * 处理认证失败错误
     */
    private Mono<Void> handleAuthenticationError(ServerWebExchange exchange, NotLoginException e) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.UNAUTHORIZED);
        response.getHeaders().add("Content-Type", MediaType.APPLICATION_JSON_VALUE);
        
        SaResult result = SaResult.error("未授权").setCode(HttpStatus.UNAUTHORIZED.value());
        
        return writeResponse(response, result);
    }
    
    /**
     * 处理通用错误
     */
    private Mono<Void> handleGenericError(ServerWebExchange exchange, Exception e) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.INTERNAL_SERVER_ERROR);
        response.getHeaders().add("Content-Type", MediaType.APPLICATION_JSON_VALUE);
        
        SaResult result = SaResult.error(e.getMessage()).setCode(HttpStatus.INTERNAL_SERVER_ERROR.value());
        
        return writeResponse(response, result);
    }
    
    /**
     * 写入响应数据
     */
    private Mono<Void> writeResponse(ServerHttpResponse response, SaResult result) {
        try {
            String json = objectMapper.writeValueAsString(result);
            DataBuffer buffer = response.bufferFactory().wrap(json.getBytes(StandardCharsets.UTF_8));
            return response.writeWith(Mono.just(buffer));
        } catch (JsonProcessingException e) {
            log.error("Error serializing response", e);
            // 如果序列化失败，返回简单的错误信息
            String fallbackJson = "{\"code\":" + result.getCode() + ",\"msg\":\"" + result.getMsg() + "\"}";
            DataBuffer buffer = response.bufferFactory().wrap(fallbackJson.getBytes(StandardCharsets.UTF_8));
            return response.writeWith(Mono.just(buffer));
        }
    }
}
